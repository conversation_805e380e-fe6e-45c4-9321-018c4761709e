# Linux Do OAuth2 快捷登录功能

## 功能概述

已成功为您的登录页面添加了Linux Do OAuth2快捷登录功能，用户可以使用Linux Do账号快速登录，未注册用户会自动注册。

## 功能特点

- ✅ 一键登录：用户点击"Linux Do 快捷登录"按钮即可跳转到Linux Do授权页面
- ✅ 自动注册：未注册用户授权后自动创建账号并登录
- ✅ 安全防护：使用state参数防止CSRF攻击
- ✅ 用户信息同步：自动获取Linux Do的用户名、昵称、邮箱等信息
- ✅ 积分奖励：新用户注册后自动获得10积分

## 配置信息

```python
# Linux Do OAuth2 配置
LINUX_DO_CLIENT_ID = 'GZfEhWWjowkChfAn5xtD6SFNfxR9YUt9'
LINUX_DO_CLIENT_SECRET = 'e5FiHlz6YwBdEJOw1VtbOYpAt7TTr4J8'
LINUX_DO_REDIRECT_URI = 'https://sd.exacg.cc/linux'
LINUX_DO_AUTH_URL = 'https://connect.linux.do/oauth2/authorize'
LINUX_DO_TOKEN_URL = 'https://connect.linux.do/oauth2/token'
LINUX_DO_USER_INFO_URL = 'https://connect.linux.do/oauth2/userinfo'
```

## 新增路由

1. **`/auth/linux_do`** - 跳转到Linux Do授权页面
2. **`/linux`** - Linux Do OAuth2回调处理（与配置的回调地址一致）

## 用户数据结构

Linux Do用户在数据库中会包含以下额外字段：

```json
{
  "username": "linuxdo_用户名",
  "password": null,
  "auth_type": "linux_do",
  "linux_do_id": "用户唯一ID",
  "linux_do_username": "Linux Do用户名",
  "linux_do_name": "用户昵称",
  "linux_do_avatar_template": "头像模板URL",
  "points": 10,
  "status": "approved"
}
```

## 登录流程

1. 用户点击"Linux Do 快捷登录"按钮
2. 跳转到Linux Do授权页面
3. 用户在Linux Do上确认授权
4. 回调到 `/linux` 路由
5. 获取授权码并换取访问令牌
6. 使用访问令牌获取用户信息
7. 检查用户是否已存在：
   - 存在：直接登录
   - 不存在：自动注册新用户并登录

## 安全特性

- 使用随机生成的state参数防止CSRF攻击
- 验证回调参数的完整性
- 安全存储用户的Linux Do ID用于身份识别
- 错误处理和用户友好的错误提示

## 用户体验

- 登录页面新增了Linux Do快捷登录按钮
- 使用绿色渐变设计，与Linux品牌色调一致
- 支持错误信息显示，用户体验友好
- 自动处理用户名冲突（添加数字后缀）

## 测试

可以运行 `test_oauth.py` 来测试OAuth2配置是否正确：

```bash
python test_oauth.py
```

## 注意事项

1. 确保服务器域名与配置的回调地址一致
2. Linux Do OAuth2需要HTTPS环境才能正常工作
3. 用户首次使用Linux Do登录时会自动创建本地账号
4. Linux Do用户的本地用户名格式为 `linuxdo_原用户名`
